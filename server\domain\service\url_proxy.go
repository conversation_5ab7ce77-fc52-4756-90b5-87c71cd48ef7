package service

import (
	"fmt"
	"socks/server/domain/entity"
	"socks/server/domain/event"
	"socks/server/util"
	"sync"
	"time"
)

var (
	urlProxyService *URLProxyService
	upsOnce         sync.Once
)

type URLProxyService struct {
	connection *event.Connection
	tunnels    map[string]*event.URLProxyTunnel
	sync.RWMutex
}

func NewURLProxyService(config *util.TunnelConfig) *URLProxyService {
	return &URLProxyService{
		connection: event.GetConnection(config),
		tunnels:    make(map[string]*event.URLProxyTunnel),
	}
}

func GetUrlProxyService(config *util.TunnelConfig) *URLProxyService {
	upsOnce.Do(func() {
		urlProxyService = NewURLProxyService(config)
	})
	return urlProxyService
}

func (s *URLProxyService) RegisterClient(client *entity.Client) error {
	return s.connection.RegisterClient(client)
}

// RegisterURLMapping 注册URL映射
func (s *URLProxyService) RegisterURLMapping(clientUUID, urlPath, baseURL, serviceName, serviceGroup, apiType string) error {
	// 检查客户端是否存在
	client := s.connection.GetClient(clientUUID)
	if client == nil {
		return fmt.Errorf("client not found: %s", clientUUID)
	}

	// 检查URL路径是否已被注册
	existingMapping := s.connection.GetURLMapping(urlPath)
	if existingMapping != nil {
		// 如果存在，且属于同一个客户端，更新映射
		// 同名不同uuid客户端暂不考虑
		if existingMapping.Client.UUID == clientUUID && !existingMapping.IsBaseUrlExist(baseURL) {
			existingMapping.BaseURL[baseURL] = struct{}{}
			return nil
		}
		return fmt.Errorf("client base url path already registered in server path: %s", urlPath)
	}

	// 创建URL映射
	mapping := &entity.URLMapping{
		Name: fmt.Sprintf("%s:%s", client.Name, serviceName),
		Client: &entity.Client{
			Name: client.Name,
			IP:   client.IP,
			UUID: client.UUID,
			Type: apiType,
		},
		URLPath:      urlPath,
		BaseURL:      map[string]struct{}{baseURL: {}},
		Created:      time.Now(),
		Enable:       true,
		Online:       true,
		ServiceName:  serviceName,
		ServiceGroup: serviceGroup,
		Description:  fmt.Sprintf("URL mapping for %s", serviceName),
		Protocol:     entity.BuildProtocol(apiType),
	}

	// 添加映射
	s.connection.AddURLMapping(mapping)
	return nil
}

// UnregisterURLMapping 取消注册URL映射
func (s *URLProxyService) UnregisterURLMapping(clientUUID, urlPath, baseURL string) error {
	// 检查映射是否存在
	mapping := s.connection.GetURLMapping(urlPath)
	if mapping == nil {
		return fmt.Errorf("url mapping not found: %s", urlPath)
	}

	// 检查是否属于该客户端
	if mapping.Client.UUID != clientUUID {
		return fmt.Errorf("url mapping does not belong to client: %s", clientUUID)
	}

	// 删除映射
	mapping.DeleteBaseUrl(baseURL)

	return nil
}

// GetURLMapping 获取URL映射
func (s *URLProxyService) GetURLMapping(urlPath string) *entity.URLMapping {
	return s.connection.GetURLMapping(urlPath)
}

// GetClientURLMappings 获取客户端的所有URL映射
func (s *URLProxyService) GetClientURLMappings(clientUUID string) []string {
	return s.connection.GetClientURLMappings(clientUUID)
}

// UpdateOnlineStatus 更新在线状态
func (s *URLProxyService) UpdateOnlineStatus(clientUUID string, online bool) error {
	s.connection.UpdateClientUrlOnlineStatus(clientUUID, online)
	return nil
}

func (s *URLProxyService) AddTunnel(clientUUID string) *event.URLProxyTunnel {
	s.Lock()
	defer s.Unlock()
	s.tunnels[clientUUID] = event.NewURLProxyTunnel(s.connection, clientUUID)
	return s.tunnels[clientUUID]
}

func (s *URLProxyService) GetTunnel(clientUUID string) *event.URLProxyTunnel {
	s.RLock()
	defer s.RUnlock()
	return s.tunnels[clientUUID]
}

func (s *URLProxyService) DeleteTunnel(clientUUID string) {
	s.Lock()
	defer s.Unlock()
	delete(s.tunnels, clientUUID)
}

func (s *URLProxyService) DeleteRespChan(clientUUID, id string) error {
	s.RLock()
	defer s.RUnlock()
	tunnel, ok := s.tunnels[clientUUID]
	if !ok {
		return fmt.Errorf("tunnel not found")
	}
	return tunnel.DeleteRespChan(id)
}

func (s *URLProxyService) SendProxyRequest(clientUUID string, message *entity.URLProxyMessage) (chan *entity.URLProxyMessage, error) {
	tunnel := s.GetTunnel(clientUUID)
	if tunnel == nil {
		tunnel = s.AddTunnel(clientUUID)
	}
	return tunnel.SendProxyRequest(message)
}
