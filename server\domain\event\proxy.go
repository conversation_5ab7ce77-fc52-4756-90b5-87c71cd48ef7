package event

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net"
	"socks/server/domain/entity"
	"time"
)

const (
	ONLINE           = true
	OFFLINE          = false
	PORT_CHAN_PREFIX = "port"
	URL_CHAN_PREFIX  = "chan"
)

// 一个基于服务端端口层面的代理监听对象，包含：
// 1、服务端代理端口监听器
// 2、服务端和客户端的持久化通讯通道
// 3、系统内的conn对象（端口映射表、通道表、监听器表）
type PortProxyTunnel struct {
	clientId   string
	clientPort int
	serverPort int
	listener   *entity.Listener // 监听serverPort的Listener
	conn       *Connection      // 系统内conn对象
	stop       chan bool
	onExit     func(int) // tunnel退出时的回调函数，参数为serverPort
}

func NewPortProxyTunnel(conn *Connection, serverPort int, clientPort int, clientId string, onExit func(int)) *PortProxyTunnel {
	return &PortProxyTunnel{
		clientId:   clientId,
		clientPort: clientPort,
		serverPort: serverPort,
		listener:   conn.GetListener(serverPort),
		conn:       conn,
		stop:       make(chan bool),
		onExit:     onExit,
	}
}

func (p *PortProxyTunnel) StartProxy() {
	p.conn.UpdatePortProxyOnlineStatus(p.clientId, p.clientPort, ONLINE)
	p.conn.UpdateLastConnectionTime(p.clientId, p.clientPort)

	// 确保在函数退出时调用回调函数进行清理
	defer func() {
		if p.onExit != nil {
			p.onExit(p.serverPort)
		}
	}()

	for {
		select {
		case <-p.stop:
			return
		default:
			// 获取用户进行访问的请求连接
			requestConn, err := p.listener.GetListener().Accept()
			if err != nil {
				log.Printf("accept error on ps=%d: %v", p.serverPort, err)
				continue
			}
			go p.StartTransData2RequestClient(requestConn)
		}
	}
}

func (p *PortProxyTunnel) StopProxy() error {
	// 当前端口映射停止传输时，仅需关闭监听对应serverPort端口的listener，无需关闭底层的tcp通道
	p.conn.CloseListener(p.serverPort)
	p.stop <- true
	return p.conn.UpdatePortProxyOnlineStatus(p.clientId, p.clientPort, OFFLINE)
}

// 转发消息至发起请求的客户端
func (p *PortProxyTunnel) StartTransData2RequestClient(conn net.Conn) {
	defer conn.Close()

	tunnel := p.conn.GetTunnelByServerPort(p.serverPort)
	ch := make(chan []byte, 100)

	// Generate unique ID
	chanId := generateProtRequestID(conn.RemoteAddr().(*net.TCPAddr).Port, p.serverPort)
	tunnel.AddRespChan(chanId, ch)
	defer tunnel.DeleteRespChan(chanId)

	// Notify client to open local connection
	if err := tunnel.WriteJSON(entity.ConnMessage{ID: chanId, Type: "open"}); err == nil {
		// TCP -> Control channel
		go func() {
			buf := make([]byte, 4096)
			for {
				n, err := conn.Read(buf)
				if n > 0 {
					tunnel.WriteJSON(entity.ConnMessage{ID: chanId, Type: "data", Data: buf[:n]})
				}
				if err != nil {
					break
				}
			}
			tunnel.WriteJSON(entity.ConnMessage{ID: chanId, Type: "close"})
		}()
	} else {
		log.Printf("write to proxy tunnel err: %v", err)
		p.StopProxy()
		return
	}

	// Control channel -> TCP
	for data := range tunnel.GetResponseChan(chanId) {
		conn.Write(data)
	}
}

// URL代理
type URLProxyTunnel struct {
	clientId string
	tunnel   *entity.SafeConn // 用来转发消息的真实通道
	conn     *Connection      // 系统内conn对象
}

func NewURLProxyTunnel(conn *Connection, clientUUID string) *URLProxyTunnel {
	return &URLProxyTunnel{
		clientId: clientUUID,
		tunnel:   conn.GetTunnel(clientUUID),
		conn:     conn,
	}
}

func (u *URLProxyTunnel) SendProxyRequest(message *entity.URLProxyMessage) (chan *entity.URLProxyMessage, error) {
	chanId := generateUrlRequestID(message.BaseURL)
	u.addRespChan(chanId)

	message.ID = chanId
	message.Type = "proxy_request"

	// 序列化代理请求
	proxyData, err := json.Marshal(message)
	if err != nil {
		// http.Error(w, "Failed to serialize proxy request", http.StatusInternalServerError)
		return nil, errors.New("failed to serialize proxy request")
	}

	// 构建消息
	tunnelMessage := entity.ConnMessage{
		ID:   message.ID,
		Type: "proxy_request",
		Data: proxyData,
	}
	err = u.tunnel.WriteJSON(tunnelMessage)
	if err != nil {
		// http.Error(w, "Failed to send request to client", http.StatusInternalServerError)
		return nil, errors.New("failed to send request to client")
	}
	return u.getResponseChan(chanId), nil
}

func (u *URLProxyTunnel) addRespChan(id string) {
	urlRespChan := make(chan *entity.URLProxyMessage, 100) // 增加缓存空间以支持流式传输
	u.tunnel.AddURLRespChan(id, urlRespChan)               // 注册通道
}

func (u *URLProxyTunnel) getResponseChan(id string) chan *entity.URLProxyMessage {
	return u.tunnel.GetURLResponseChan(id)
}

func (u *URLProxyTunnel) DeleteRespChan(id string) error {
	return u.tunnel.DeleteURLRespChan(id)
}

func generateProtRequestID(userPort, serverPort int) string {
	return fmt.Sprintf("%s-%d-%d-%d", PORT_CHAN_PREFIX, time.Now().UnixNano(), userPort, serverPort)
}

func generateUrlRequestID(baseUrl string) string {
	return fmt.Sprintf("%s-%d-%s", URL_CHAN_PREFIX, time.Now().UnixNano(), baseUrl)
}
