package service

import (
	"socks/server/application/event"
	"socks/server/domain/entity"
	"socks/server/util"
)

type UrlProxyService interface {
	RegisterClient(client *entity.Client) error
	RegisterURLMapping(clientUUID, urlPath, baseURL, serviceName, serviceGroup, apiType string) error
	UnregisterURLMapping(clientUUID, urlPath, baseURL string) error
	GetURLMapping(urlPath string) *entity.URLMapping
	GetClientURLMappings(clientUUID string) []string
	UpdateOnlineStatus(clientUUID string, online bool) error
	SendProxyRequest(clientUUID string, message *entity.URLProxyMessage) (chan *entity.URLProxyMessage, error)
	DeleteRespChan(clientUUID, id string) error
}

func GetUrlProxyService(config *util.TunnelConfig) UrlProxyService {
	return event.GetURLProxy(config)
}
