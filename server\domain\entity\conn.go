package entity

import (
	"bufio"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"sync"
	"time"
)

var (
	tunnelGroup *TunnelGroup
	scgOnce     sync.Once
)

// Message 定义了 TCP/UDP 上传输的消息格式，用于多路复用
type ConnMessage struct {
	ID   string `json:"id"`             // 连接 ID
	Type string `json:"type"`           // open, data, close, proxy_request, proxy_response
	Data []byte `json:"data,omitempty"` // 二进制数据
}

// URLProxyMessage 定义了URL代理消息格式
type URLProxyMessage struct {
	ConnMessage
	BaseURL   string            `json:"base_url,omitempty"`   // 客户端映射的URL根路径，用来发送给客户端查找真实ip
	TargetURL string            `json:"target_url,omitempty"` // 目标URL，指定BaseURL下的目标url，包含BaseURL的部分
	Method    string            `json:"method,omitempty"`     // HTTP方法
	Headers   map[string]string `json:"headers,omitempty"`    // HTTP头
	Body      []byte            `json:"body,omitempty"`       // 请求/响应体
	Status    int               `json:"status,omitempty"`     // HTTP状态码（响应时使用）
	Error     string            `json:"error,omitempty"`      // 错误信息

	// 流式传输相关字段
	IsStream    bool   `json:"is_stream,omitempty"`     // 是否为流式传输
	StreamType  string `json:"stream_type,omitempty"`   // 流类型：stream_start, stream_chunk, stream_end
	ChunkIndex  int    `json:"chunk_index,omitempty"`   // 数据块索引
	IsLastChunk bool   `json:"is_last_chunk,omitempty"` // 是否为最后一个数据块
}

// SafeConn 包装 TCP 连接，保证并发写安全
type SafeConn struct {
	conn net.Conn // 底层 TCP 连接
	wmu  sync.Mutex
	enc  *json.Encoder
	dec  *json.Decoder
	bw   *bufio.Writer // 添加缓冲写入器

	respChans    map[string]chan []byte // 多路复用
	urlRespChans map[string]chan *URLProxyMessage
	rwm          sync.RWMutex
}

func NewSafeConn(conn net.Conn) *SafeConn {

	if tcpConn, ok := conn.(*net.TCPConn); ok {
		tcpConn.SetNoDelay(true)   // 禁用Nagle算法
		tcpConn.SetKeepAlive(true) // 启用TCP keepalive
		tcpConn.SetKeepAlivePeriod(30 * time.Second)
		tcpConn.SetWriteBuffer(64 * 1024) // 增大写缓冲区
		tcpConn.SetReadBuffer(64 * 1024)  // 增大读缓冲区
	}
	bw := bufio.NewWriterSize(conn, 32*1024)
	return &SafeConn{
		conn:         conn,
		bw:           bw,
		enc:          json.NewEncoder(bw),
		dec:          json.NewDecoder(bufio.NewReaderSize(conn, 32*1024)),
		respChans:    make(map[string]chan []byte),
		urlRespChans: make(map[string]chan *URLProxyMessage),
	}
}

// 往通道中写入数据
func (c *SafeConn) WriteJSON(v interface{}) error {
	c.wmu.Lock()
	defer c.wmu.Unlock()
	err := c.enc.Encode(v)
	// 对所有消息类型都立即刷新缓冲区
	if err == nil && c.bw != nil {
		err = c.bw.Flush()
	}
	if bw, ok := c.conn.(interface{ Flush() error }); ok {
		bw.Flush()
	}
	return err
}

// 往通道中读取数据
func (c *SafeConn) ReadJSON(v interface{}) error {
	return c.dec.Decode(v)
}

// 关闭通道
func (c *SafeConn) Close() error {
	c.rwm.Lock()
	defer c.rwm.Unlock()
	for id, ch := range c.respChans {
		close(ch)
		delete(c.respChans, id)
	}
	return c.conn.Close()
}

func (c *SafeConn) Flush() {
	c.bw.Flush()
}

func (c *SafeConn) AddRespChan(id string, ch chan []byte) error {
	c.rwm.Lock()
	defer c.rwm.Unlock()
	if _, ok := c.respChans[id]; ok {
		return fmt.Errorf("chan id repeat: %s", id)
	}
	c.respChans[id] = ch
	return nil
}

func (c *SafeConn) GetResponseChan(id string) chan []byte {
	c.rwm.RLock()
	defer c.rwm.RUnlock()
	return c.respChans[id]
}

func (c *SafeConn) DeleteRespChan(id string) error {
	c.rwm.Lock()
	defer c.rwm.Unlock()
	if _, ok := c.respChans[id]; !ok {
		return fmt.Errorf("try to delete an nil chan")
	}
	delete(c.respChans, id)
	return nil
}

func (c *SafeConn) AddURLRespChan(id string, ch chan *URLProxyMessage) error {
	c.rwm.Lock()
	defer c.rwm.Unlock()
	if _, ok := c.urlRespChans[id]; ok {
		return fmt.Errorf("chan id repeat: %s", id)
	}
	c.urlRespChans[id] = ch
	return nil
}

func (c *SafeConn) GetURLResponseChan(id string) chan *URLProxyMessage {
	c.rwm.RLock()
	defer c.rwm.RUnlock()
	return c.urlRespChans[id]
}

func (c *SafeConn) DeleteURLRespChan(id string) error {
	c.rwm.Lock()
	defer c.rwm.Unlock()
	if _, ok := c.urlRespChans[id]; !ok {
		return fmt.Errorf("try to delete an nil chan")
	}
	delete(c.urlRespChans, id)
	return nil
}

type TunnelGroup struct {
	Conns  map[string]*SafeConn
	Locker sync.RWMutex
}

func GetTunnelGroup() *TunnelGroup {
	scgOnce.Do(func() {
		tunnelGroup = &TunnelGroup{
			Conns: make(map[string]*SafeConn),
		}
	})
	return tunnelGroup
}

func (g *TunnelGroup) AddTunnel(uuid string, conn *SafeConn) {
	g.Locker.Lock()
	defer g.Locker.Unlock()

	g.Conns[uuid] = conn
}

func (g *TunnelGroup) GetTunnel(uuid string) *SafeConn {
	g.Locker.RLock()
	defer g.Locker.RUnlock()
	if conn, ok := g.Conns[uuid]; ok {
		return conn
	}
	return nil
}

func (g *TunnelGroup) CloseTunnel(uuid string) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	if conn, ok := g.Conns[uuid]; ok {
		err := conn.Close()
		if err != nil {
			log.Printf("close tunnel error: %v", err)
		}

		delete(g.Conns, uuid)
	}
}
