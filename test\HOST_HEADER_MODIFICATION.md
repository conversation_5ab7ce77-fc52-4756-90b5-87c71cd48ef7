# Host请求头修改功能实现

## 功能概述

修改了URL代理的实现方式，现在服务端不再构建HTTP请求数据，而是直接转发原始请求数据给客户端。客户端接收到原始请求数据后，会解析并修改Host请求头为`localhost:port`（port来自mapping配置），然后转发给本地连接。

## 主要改进

### 1. 服务端修改

**原来的实现：**
- 服务端构建完整的HTTP请求数据，包括修改请求行中的路径
- 添加所有请求头，但可能修改Host头

**新的实现：**
- **直接转发原始数据**：服务端完全不修改HTTP请求数据
- **保持原始格式**：使用`r.RequestURI`而不是`targetPath`，保持原始请求URI
- **透明转发**：所有请求头都保持原始状态转发给客户端

### 2. 客户端修改

**原来的实现：**
- 客户端直接将接收到的数据转发给本地连接
- 不对HTTP请求数据进行任何处理

**新的实现：**
- **智能识别**：区分URL代理连接（ID以"url_"开头）和普通端口代理连接
- **HTTP解析**：解析HTTP请求数据，分离请求头和请求体
- **Host头修改**：将Host头修改为`localhost:port`，port来自URL映射配置
- **重新构建**：重新构建HTTP请求并转发给本地连接

## 技术实现

### 服务端修改

<augment_code_snippet path="server/server/handlers/url_proxy.go" mode="EXCERPT">
```go
// 直接转发原始HTTP请求数据，不进行任何修改
// 重新构建请求体，因为之前被读取过
var requestData []byte

// 构建原始HTTP请求（保持原始格式）
requestData = append(requestData, []byte(fmt.Sprintf("%s %s %s\r\n", r.Method, r.RequestURI, r.Proto))...)

// 添加所有原始请求头（完全不修改）
for name, values := range r.Header {
    for _, v := range values {
        requestData = append(requestData, []byte(fmt.Sprintf("%s: %s\r\n", name, v))...)
    }
}

// 添加空行分隔头部和正文
requestData = append(requestData, []byte("\r\n")...)

// 添加请求正文
if r.Body != nil {
    body, err := io.ReadAll(r.Body)
    if err == nil {
        requestData = append(requestData, body...)
    }
}
```
</augment_code_snippet>

### 客户端修改

<augment_code_snippet path="client/main.go" mode="EXCERPT">
```go
// handleDataMessage 处理数据消息
func handleDataMessage(msg Message, cm *ConnectionManager) {
    if localConn := cm.Get(msg.ID); localConn != nil {
        // 检查是否是URL代理连接
        if strings.HasPrefix(msg.ID, "url_") {
            // URL代理连接需要处理Host头
            handleURLDataMessage(msg, localConn)
        } else {
            // 普通端口代理连接直接转发
            localConn.Write(msg.Data)
        }
    }
}

// handleURLDataMessage 处理URL代理的数据消息，修改Host头
func handleURLDataMessage(msg Message, localConn net.Conn) {
    // 解析HTTP请求数据
    requestData := string(msg.Data)
    
    // 分离请求头和请求体
    parts := strings.Split(requestData, "\r\n\r\n")
    if len(parts) < 1 {
        log.Printf("无效的HTTP请求数据: %s", msg.ID)
        return
    }
    
    headerPart := parts[0]
    var bodyPart string
    if len(parts) > 1 {
        bodyPart = strings.Join(parts[1:], "\r\n\r\n")
    }
    
    // 解析请求头
    lines := strings.Split(headerPart, "\r\n")
    if len(lines) < 1 {
        log.Printf("无效的HTTP请求头: %s", msg.ID)
        return
    }
    
    // 获取请求行
    requestLine := lines[0]
    
    // 构建新的请求头，修改Host
    var newHeaders []string
    newHeaders = append(newHeaders, requestLine)
    
    // 获取目标端口（从URL映射中获取）
    var targetPort int
    urlMappingsMux.Lock()
    for _, mapping := range urlMappings {
        targetPort = mapping.Port
        break // 使用第一个映射的端口
    }
    urlMappingsMux.Unlock()
    
    if targetPort == 0 {
        targetPort = 80 // 默认端口
    }
    
    // 添加修改后的Host头
    newHeaders = append(newHeaders, fmt.Sprintf("Host: localhost:%d", targetPort))
    
    // 添加其他请求头（跳过原始的Host头）
    for i := 1; i < len(lines); i++ {
        line := lines[i]
        if !strings.HasPrefix(strings.ToLower(line), "host:") {
            newHeaders = append(newHeaders, line)
        }
    }
    
    // 重新构建HTTP请求
    newRequest := strings.Join(newHeaders, "\r\n") + "\r\n\r\n" + bodyPart
    
    log.Printf("修改Host头后的请求 (连接ID: %s):\n%s", msg.ID, newRequest)
    
    // 发送修改后的请求到本地连接
    if _, err := localConn.Write([]byte(newRequest)); err != nil {
        log.Printf("发送请求到本地连接失败: %s, %v", msg.ID, err)
    }
}
```
</augment_code_snippet>

## 工作流程

### 请求处理流程

1. **服务端接收请求**：接收来自外部的HTTP请求
2. **查找URL映射**：根据请求路径查找对应的客户端映射
3. **发送url_open消息**：告知客户端建立连接，包含baseURL和targetPath
4. **转发原始请求**：将完全未修改的HTTP请求数据发送给客户端
5. **客户端解析请求**：客户端解析HTTP请求数据
6. **修改Host头**：将Host头修改为`localhost:映射端口`
7. **转发到本地服务**：将修改后的请求发送给本地服务
8. **响应回传**：本地服务的响应通过相同路径回传给原始请求者

### 数据流示例

```
原始请求:
GET /api/chat HTTP/1.1
Host: proxy.example.com:8080
Content-Type: application/json
User-Agent: curl/7.68.0

{"message": "hello"}

↓ 服务端直接转发 ↓

客户端接收后修改:
GET /api/chat HTTP/1.1
Host: localhost:3000          ← 修改为本地服务端口
Content-Type: application/json
User-Agent: curl/7.68.0

{"message": "hello"}

↓ 转发给本地服务 ↓
```

## 优势

1. **更好的兼容性**：保持原始请求格式，减少兼容性问题
2. **精确的Host控制**：客户端可以精确控制Host头，确保本地服务正确识别
3. **简化服务端逻辑**：服务端不需要理解HTTP协议细节，只做透明转发
4. **灵活的端口映射**：每个URL映射可以有不同的本地端口
5. **更好的调试**：客户端日志显示修改前后的请求，便于调试

## 配置要求

客户端需要有URL映射配置，例如：

```json
{
  "http://localhost:8080/api": {
    "host": "localhost",
    "port": 3000
  }
}
```

这样当代理`http://localhost:8080/api/*`的请求时，Host头会被修改为`localhost:3000`。

## 测试验证

可以通过以下方式验证功能：

1. **启动本地服务**：在端口3000启动一个HTTP服务
2. **配置URL映射**：将某个URL路径映射到localhost:3000
3. **发送代理请求**：通过代理服务器发送请求
4. **检查日志**：查看客户端日志中的Host头修改情况
5. **验证响应**：确认本地服务正确接收并响应请求

## 注意事项

1. **端口获取逻辑**：当前实现使用第一个URL映射的端口，如果有多个映射可能需要更精确的匹配逻辑
2. **错误处理**：如果HTTP请求格式不正确，会记录错误日志但不会中断连接
3. **性能考虑**：每个请求都需要解析和重构HTTP数据，对性能有一定影响
4. **内存使用**：需要将整个HTTP请求加载到内存中进行处理
