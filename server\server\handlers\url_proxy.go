package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"socks/server/application/service"
	"socks/server/domain/entity"
	"socks/server/server/dto"
	"socks/server/util"
	"strings"
	"sync"
	"time"
	"unicode/utf8"
)

var (
	urlProxyHandler       *URLProxyHandler
	uphOnce               sync.Once
	DefaultRequestTimeout = 600 * time.Second // s
)

type URLProxyHandler struct {
	urlProxyService service.UrlProxyService
}

func GetURLProxyHandler() *URLProxyHandler {
	uphOnce.Do(func() {
		urlProxyHandler = &URLProxyHandler{
			urlProxyService: service.GetUrlProxyService(util.SystemConfig),
		}
	})

	return urlProxyHandler
}

// RegisterURLHandler 处理URL注册请求
func (h *URLProxyHandler) RegisterURLHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req dto.URLRegisterRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "illegal request body", http.StatusBadRequest)
		return
	}

	if req.ClientUUID == "" {
		http.Error(w, "missing client_id", http.StatusBadRequest)
		return
	}

	if req.ApiType == "" {
		http.Error(w, "missing api_type", http.StatusBadRequest)
		return
	}

	if req.ServiceName == "" {
		http.Error(w, "missing service_name", http.StatusBadRequest)
		return
	}

	if req.BaseURL == "" {
		http.Error(w, "missing base_url", http.StatusBadRequest)
		return
	}

	// 根据客户端类型生成代理路径
	var urlPath string
	switch req.ApiType {
	case "AGENT":
		urlPath = fmt.Sprintf("/Tunnel/Agent/%s/%s", req.ClientName, req.ServiceName)
	case "AI":
		urlPath = fmt.Sprintf("/Tunnel/AI/%s/%s/%s", req.ServiceGroup, req.ServiceName, req.ClientName)
	case "API":
		urlPath = fmt.Sprintf("/Tunnel/API/%s/%s/%s", req.ServiceGroup, req.ServiceName, req.ClientName)
	default:
		http.Error(w, "invalid client_type", http.StatusBadRequest)
		return
	}

	// 注册URL映射，使用通配符匹配所有子路径
	err := h.urlProxyService.RegisterURLMapping(
		req.ClientUUID,
		urlPath,
		req.BaseURL,
		req.ServiceName,
		req.ServiceGroup,
		req.ApiType,
	)

	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(dto.URLRegisterResponse{
		Success: true,
		URLPath: urlPath,
	})

	log.Printf("URL register success: %s -> %s (client: %s)", urlPath, req.BaseURL, req.ClientUUID)
}

// UnregisterURLHandler 处理URL取消注册请求
func (h *URLProxyHandler) UnregisterURLHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求参数
	clientUUID := r.URL.Query().Get("client_id")
	if clientUUID == "" {
		http.Error(w, "missing client_id", http.StatusBadRequest)
		return
	}

	urlPath := r.URL.Query().Get("url_path")
	if urlPath == "" {
		http.Error(w, "missing url_path", http.StatusBadRequest)
		return
	}

	baseURL := r.URL.Query().Get("base_url")
	if baseURL == "" {
		http.Error(w, "missing base_url", http.StatusBadRequest)
		return
	}

	// 取消注册URL映射
	err := h.urlProxyService.UnregisterURLMapping(clientUUID, urlPath, baseURL)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":  true,
		"url_path": urlPath,
	})

	log.Printf("URL unregister success: %s (cilent: %s)", urlPath, clientUUID)
}

// ProxyHandler 处理代理请求 - 使用TCP连接转发模式
func (h *URLProxyHandler) ProxyHandler(w http.ResponseWriter, r *http.Request) {
	// 获取请求路径
	url := r.URL.Path
	queryParam := r.URL.RawQuery
	// 查找映射并构建目标路径
	mapping, targetPath, baseURL, err := h.lookupMappingAndBuildPath(url)
	if err != nil {
		http.Error(w, err.Error(), http.StatusNotFound)
		return
	}

	// 检查映射是否在线
	if !mapping.Online {
		http.Error(w, "Service offline", http.StatusServiceUnavailable)
		return
	}

	// 添加查询参数
	if queryParam != "" {
		targetPath = targetPath + "?" + queryParam
	}

	// 获取客户端的SafeConn连接
	tunnel := h.urlProxyService.GetTunnel(mapping.Client.UUID)
	if tunnel == nil {
		http.Error(w, "Client tunnel not found", http.StatusServiceUnavailable)
		return
	}

	// 启动TCP连接转发
	h.startTCPForward(w, r, tunnel, baseURL, targetPath)
}

// startTCPForward 启动TCP连接转发，类似端口代理的方式
func (h *URLProxyHandler) startTCPForward(w http.ResponseWriter, r *http.Request, tunnel *entity.SafeConn, baseURL, targetPath string) {
	// 生成唯一的连接ID
	connID := fmt.Sprintf("url_%d_%s", time.Now().UnixNano(), r.RemoteAddr)
	// 创建响应通道
	respChan := make(chan []byte, 100)
	tunnel.AddRespChan(connID, respChan)
	defer func() {
		// 安全地关闭通道：先检查通道是否还存在，避免重复关闭
		if ch := tunnel.GetResponseChan(connID); ch != nil {
			// 尝试关闭通道，如果已经关闭会panic，所以用recover保护
			func() {
				defer func() {
					if r := recover(); r != nil {
						log.Printf("通道已经关闭: %s", connID)
					}
				}()
				close(ch)
			}()
		}
		tunnel.DeleteRespChan(connID)
	}()

	// 发送打开连接消息给客户端
	openMsg := entity.ConnMessage{
		ID:   connID,
		Type: "url_open",
		Data: []byte(fmt.Sprintf("%s|%s", baseURL, targetPath)), // 传递baseURL和targetPath
	}

	if err := tunnel.WriteJSON(openMsg); err != nil {
		http.Error(w, "Failed to send open message", http.StatusInternalServerError)
		return
	}

	// 直接转发原始HTTP请求数据，完全不修改
	var requestData []byte

	// 构建原始HTTP请求，传递targetPath
	requestData = append(requestData, []byte(fmt.Sprintf("%s %s %s\r\n", r.Method, targetPath, r.Proto))...)

	// 添加所有原始请求头（完全不修改）
	for name, values := range r.Header {
		for _, v := range values {
			requestData = append(requestData, []byte(fmt.Sprintf("%s: %s\r\n", name, v))...)
		}
	}

	// 添加空行分隔头部和正文
	requestData = append(requestData, []byte("\r\n")...)

	// 添加请求正文
	if r.Body != nil {
		body, err := io.ReadAll(r.Body)
		if err == nil {
			requestData = append(requestData, body...)
		}
	}

	// 打印完全原始的HTTP请求数据
	if utf8.Valid(requestData) {
		log.Printf("直接转发原始HTTP请求给客户端:\n%s", string(requestData))
	} else {
		log.Printf("直接转发原始HTTP请求给客户端（包含二进制内容），长度: %d 字节", len(requestData))
	}

	dataMsg := entity.ConnMessage{
		ID:   connID,
		Type: "data",
		Data: requestData,
	}

	if err := tunnel.WriteJSON(dataMsg); err != nil {
		http.Error(w, "Failed to send request data", http.StatusInternalServerError)
		return
	}

	// 发送关闭消息（表示请求数据发送完毕）
	closeMsg := entity.ConnMessage{
		ID:   connID,
		Type: "close",
	}

	if err := tunnel.WriteJSON(closeMsg); err != nil {
		log.Printf("Failed to send close message: %v", err)
	}
	// 等待并提取HTTP响应体数据
	var responseBuffer bytes.Buffer
	headersParsed := false

	for data := range respChan {
		if len(data) > 0 {
			responseBuffer.Write(data)

			// 如果还没有跳过响应头，寻找头部结束标记
			if !headersParsed {
				// 检查是否包含完整的HTTP响应头（以\r\n\r\n结束）
				bufferData := responseBuffer.Bytes()
				headerEndIndex := bytes.Index(bufferData, []byte("\r\n\r\n"))

				if headerEndIndex != -1 {
					// 找到头部和body的分界点，直接提取body部分
					bodyStart := headerEndIndex + 4
					bodyData := bufferData[bodyStart:]

					// 直接写入body到响应，不解析HTTP头部
					if len(bodyData) > 0 {
						w.Write(bodyData)
						if flusher, ok := w.(http.Flusher); ok {
							flusher.Flush()
						}
						log.Printf("写入响应体数据: %d bytes", len(bodyData))
					}

					headersParsed = true
					responseBuffer.Reset() // 清空缓冲区
				}
			} else {
				// 头部已经跳过，直接写入body数据
				w.Write(data)
				if flusher, ok := w.(http.Flusher); ok {
					flusher.Flush()
				}
				log.Printf("写入响应体数据: %d bytes", len(data))
			}
		}
	}
}

// lookupMappingAndBuildPath 查找映射并构建目标路径
func (h *URLProxyHandler) lookupMappingAndBuildPath(urlPath string) (*entity.URLMapping, string, string, error) {
	urlParts := strings.Split(strings.Trim(urlPath, "/"), "/")
	if len(urlParts) < 4 {
		return nil, "", "", fmt.Errorf("invalid URL path")
	}

	apiType := strings.ToLower(urlParts[1])
	var mapping *entity.URLMapping
	var targetPath string

	// 根据客户端类型查找匹配的映射
	switch apiType {
	case "agent":
		if len(urlParts) >= 4 {
			// Agent路径格式: /Agent/客户端名称/服务名称/...
			urlPath := fmt.Sprintf("/%s/%s/%s/%s", urlParts[0], urlParts[1], urlParts[2], urlParts[3])
			mapping = h.urlProxyService.GetURLMapping(urlPath)
			if len(urlParts) > 4 {
				targetPath = "/" + strings.Join(urlParts[4:], "/")
			} else {
				targetPath = "/"
			}
		}
	case "ai", "api":
		if len(urlParts) >= 5 {
			// AI/API路径格式: /(AI|API)/服务组别/服务名称/客户端名称/...
			urlPath := fmt.Sprintf("/%s/%s/%s/%s/%s", urlParts[0], urlParts[1], urlParts[2], urlParts[3], urlParts[4])
			mapping = h.urlProxyService.GetURLMapping(urlPath)
			if len(urlParts) > 5 {
				targetPath = "/" + strings.Join(urlParts[5:], "/")
			} else {
				targetPath = "/"
			}
		}
	default:
		return nil, "", "", fmt.Errorf("unsupported API type: %s", apiType)
	}

	if mapping == nil {
		return nil, "", "", fmt.Errorf("URL mapping not found")
	}

	baseURL := mapping.MatchBaseURL(targetPath)
	return mapping, targetPath, baseURL, nil
}
