package handlers

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"socks/server/application/service"
	"socks/server/domain/entity"
	"socks/server/server/dto"
	"socks/server/util"
	"strings"
	"sync"
	"time"
)

var (
	urlProxyHandler       *URLProxyHandler
	uphOnce               sync.Once
	DefaultRequestTimeout = 600 * time.Second // s
)

type URLProxyHandler struct {
	urlProxyService service.UrlProxyService
}

func GetURLProxyHandler() *URLProxyHandler {
	uphOnce.Do(func() {
		urlProxyHandler = &URLProxyHandler{
			urlProxyService: service.GetUrlProxyService(util.SystemConfig),
		}
	})

	return urlProxyHandler
}

// RegisterURLHandler 处理URL注册请求
func (h *URLProxyHandler) RegisterURLHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req dto.URLRegisterRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "illegal request body", http.StatusBadRequest)
		return
	}

	if req.ClientUUID == "" {
		http.Error(w, "missing client_id", http.StatusBadRequest)
		return
	}

	if req.ApiType == "" {
		http.Error(w, "missing api_type", http.StatusBadRequest)
		return
	}

	if req.ServiceName == "" {
		http.Error(w, "missing service_name", http.StatusBadRequest)
		return
	}

	if req.BaseURL == "" {
		http.Error(w, "missing base_url", http.StatusBadRequest)
		return
	}

	// 根据客户端类型生成代理路径
	var urlPath string
	switch req.ApiType {
	case "AGENT":
		urlPath = fmt.Sprintf("/Tunnel/Agent/%s/%s", req.ClientName, req.ServiceName)
	case "AI":
		urlPath = fmt.Sprintf("/Tunnel/AI/%s/%s/%s", req.ServiceGroup, req.ServiceName, req.ClientName)
	case "API":
		urlPath = fmt.Sprintf("/Tunnel/API/%s/%s/%s", req.ServiceGroup, req.ServiceName, req.ClientName)
	default:
		http.Error(w, "invalid client_type", http.StatusBadRequest)
		return
	}

	// 注册URL映射，使用通配符匹配所有子路径
	err := h.urlProxyService.RegisterURLMapping(
		req.ClientUUID,
		urlPath,
		req.BaseURL,
		req.ServiceName,
		req.ServiceGroup,
		req.ApiType,
	)

	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(dto.URLRegisterResponse{
		Success: true,
		URLPath: urlPath,
	})

	log.Printf("URL register success: %s -> %s (client: %s)", urlPath, req.BaseURL, req.ClientUUID)
}

// UnregisterURLHandler 处理URL取消注册请求
func (h *URLProxyHandler) UnregisterURLHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodDelete {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求参数
	clientUUID := r.URL.Query().Get("client_id")
	if clientUUID == "" {
		http.Error(w, "missing client_id", http.StatusBadRequest)
		return
	}

	urlPath := r.URL.Query().Get("url_path")
	if urlPath == "" {
		http.Error(w, "missing url_path", http.StatusBadRequest)
		return
	}

	baseURL := r.URL.Query().Get("base_url")
	if baseURL == "" {
		http.Error(w, "missing base_url", http.StatusBadRequest)
		return
	}

	// 取消注册URL映射
	err := h.urlProxyService.UnregisterURLMapping(clientUUID, urlPath, baseURL)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回成功响应
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"success":  true,
		"url_path": urlPath,
	})

	log.Printf("URL unregister success: %s (cilent: %s)", urlPath, clientUUID)
}

// ProxyHandler 处理代理请求
func (h *URLProxyHandler) ProxyHandler(w http.ResponseWriter, r *http.Request) {
	// 获取请求路径
	url := r.URL.Path
	queryParam := r.URL.RawQuery
	// 查找映射并构建目标路径
	mapping, targetPath, baseURL, err := h.lookupMappingAndBuildPath(url)
	if err != nil {
		http.Error(w, err.Error(), http.StatusNotFound)
		return
	}

	// 检查映射是否在线
	if !mapping.Online {
		http.Error(w, "Service offline", http.StatusServiceUnavailable)
		return
	}

	// 添加查询参数
	if queryParam != "" {
		targetPath = targetPath + "?" + queryParam
	}

	// 检测是否需要流式处理

	// 构建代理请求消息
	proxyRequest := &entity.URLProxyMessage{
		BaseURL:   baseURL,
		TargetURL: targetPath,
		Method:    r.Method,
		Headers:   make(map[string]string),
		Body:      nil,
		IsStream:  false,
	}

	// 读取请求体
	if r.Body != nil {
		body, err := io.ReadAll(r.Body)
		if err != nil {
			http.Error(w, "Failed to read request body", http.StatusInternalServerError)
			return
		}
		proxyRequest.Body = body
		proxyRequest.IsStream = h.isStreamRequest(r, body)
	}

	// 复制请求头
	for name, values := range r.Header {
		if len(values) > 0 {
			proxyRequest.Headers[name] = values[0]
		}
	}

	responseChan, err := h.urlProxyService.SendProxyRequest(mapping.Client.UUID, proxyRequest)
	if err != nil {
		http.Error(w, "Failed to send request to client", http.StatusInternalServerError)
		return
	}

	if proxyRequest.IsStream {
		h.handleStreamResponse(w, responseChan, mapping.Client.UUID, proxyRequest.ID)
	} else {
		h.handleNormalResponse(w, responseChan, mapping.Client.UUID, proxyRequest.ID)
	}
}

// lookupMappingAndBuildPath 查找映射并构建目标路径
func (h *URLProxyHandler) lookupMappingAndBuildPath(urlPath string) (*entity.URLMapping, string, string, error) {
	urlParts := strings.Split(strings.Trim(urlPath, "/"), "/")
	if len(urlParts) < 4 {
		return nil, "", "", fmt.Errorf("invalid URL path")
	}

	apiType := strings.ToLower(urlParts[1])
	var mapping *entity.URLMapping
	var targetPath string

	// 根据客户端类型查找匹配的映射
	switch apiType {
	case "agent":
		if len(urlParts) >= 4 {
			// Agent路径格式: /Agent/客户端名称/服务名称/...
			urlPath := fmt.Sprintf("/%s/%s/%s/%s", urlParts[0], urlParts[1], urlParts[2], urlParts[3])
			mapping = h.urlProxyService.GetURLMapping(urlPath)
			if len(urlParts) > 4 {
				targetPath = "/" + strings.Join(urlParts[4:], "/")
			} else {
				targetPath = "/"
			}
		}
	case "ai", "api":
		if len(urlParts) >= 5 {
			// AI/API路径格式: /(AI|API)/服务组别/服务名称/客户端名称/...
			urlPath := fmt.Sprintf("/%s/%s/%s/%s/%s", urlParts[0], urlParts[1], urlParts[2], urlParts[3], urlParts[4])
			mapping = h.urlProxyService.GetURLMapping(urlPath)
			if len(urlParts) > 5 {
				targetPath = "/" + strings.Join(urlParts[5:], "/")
			} else {
				targetPath = "/"
			}
		}
	default:
		return nil, "", "", fmt.Errorf("unsupported API type: %s", apiType)
	}

	if mapping == nil {
		return nil, "", "", fmt.Errorf("URL mapping not found")
	}

	baseURL := mapping.MatchBaseURL(targetPath)
	return mapping, targetPath, baseURL, nil
}

// isStreamRequest 检测请求是否需要流式处理
func (h *URLProxyHandler) isStreamRequest(r *http.Request, body []byte) bool {
	// 检查Accept头是否包含text/event-stream
	accept := r.Header.Get("Accept")
	if strings.Contains(accept, "text/event-stream") {
		return true
	}

	// 检查是否包含stream参数
	if r.URL.Query().Get("stream") == "true" {
		return true
	}

	// 检查Content-Type是否表明这是一个可能需要流式响应的请求
	contentType := r.Header.Get("Content-Type")
	if strings.Contains(contentType, "application/json") {
		if len(body) > 0 {
			var reqBody map[string]interface{}
			if err := json.Unmarshal(body, &reqBody); err == nil {
				if reqBody["stream"] == true {
					return true
				}
			}
		}
	}

	return false
}

// handleNormalResponse 处理普通响应
func (h *URLProxyHandler) handleNormalResponse(w http.ResponseWriter, responseChan chan *entity.URLProxyMessage, clientUUID, requestID string) {
	defer func() {
		err := h.urlProxyService.DeleteRespChan(clientUUID, requestID)
		if err != nil {
			log.Printf("delete resp chan fail: %v", err)
		}
	}()

	// 等待客户端响应（30秒超时）
	select {
	case response := <-responseChan:
		// 处理响应
		if response.Error != "" {
			http.Error(w, response.Error, http.StatusInternalServerError)
			return
		}

		// 设置响应头
		for name, value := range response.Headers {
			w.Header().Set(name, value)
		}

		// 设置状态码
		w.WriteHeader(response.Status)

		// 写入响应体
		if len(response.Body) > 0 {
			w.Write(response.Body)
		}

	case <-time.After(DefaultRequestTimeout):
		http.Error(w, "Request timeout", http.StatusGatewayTimeout)
	}
}

// handleStreamResponse 处理流式响应
func (h *URLProxyHandler) handleStreamResponse(w http.ResponseWriter, responseChan chan *entity.URLProxyMessage, clientUUID, requestID string) {
	defer func() {
		err := h.urlProxyService.DeleteRespChan(clientUUID, requestID)
		if err != nil {
			log.Printf("delete resp chan fail: %v", err)
		}
	}()

	// 设置流式响应头
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Headers", "Cache-Control")

	// 获取Flusher接口用于实时推送数据
	flusher, ok := w.(http.Flusher)
	if !ok {
		http.Error(w, "Streaming unsupported", http.StatusInternalServerError)
		return
	}

	// 发送初始响应头
	w.WriteHeader(http.StatusOK)
	flusher.Flush()

	// 处理流式响应
	timeout := time.NewTimer(DefaultRequestTimeout)
	defer timeout.Stop()

	for {
		select {
		case response := <-responseChan:
			if response == nil {
				return
			}

			// 处理错误
			if response.Error != "" {
				// 发送错误事件
				fmt.Fprintf(w, "event: error\ndata: %s\n\n", response.Error)
				flusher.Flush()
				return
			}

			// 处理流式数据
			if response.IsStream {
				switch response.StreamType {
				case "stream_start":
					// 流开始，设置额外的响应头
					for name, value := range response.Headers {
						if name != "Content-Type" && name != "Cache-Control" && name != "Connection" {
							w.Header().Set(name, value)
						}
					}

				case "stream_chunk":
					// 发送数据块
					if len(response.Body) > 0 {
						fmt.Fprintf(w, "data: %s\n\n", string(response.Body))
						flusher.Flush()
					}

				case "stream_end":
					// 流结束
					if len(response.Body) > 0 {
						fmt.Fprintf(w, "data: %s\n\n", string(response.Body))
					}
					fmt.Fprintf(w, "event: end\ndata: [DONE]\n\n")
					flusher.Flush()
					return
				}
			} else {
				// 非流式响应，直接发送数据
				if len(response.Body) > 0 {
					fmt.Fprintf(w, "data: %s\n\n", string(response.Body))
					flusher.Flush()
				}
				return
			}

			// 重置超时
			timeout.Reset(DefaultRequestTimeout)

		case <-timeout.C:
			fmt.Fprintf(w, "event: error\ndata: Request timeout\n\n")
			flusher.Flush()
			return
		}
	}
}
