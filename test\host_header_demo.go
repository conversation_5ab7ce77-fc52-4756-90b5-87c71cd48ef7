package main

import (
	"fmt"
	"log"
	"strings"
)

// 模拟handleURLDataMessage函数的Host头修改逻辑
func modifyHostHeader(requestData string, targetPort int) string {
	// 分离请求头和请求体
	parts := strings.Split(requestData, "\r\n\r\n")
	if len(parts) < 1 {
		log.Printf("无效的HTTP请求数据")
		return requestData
	}
	
	headerPart := parts[0]
	var bodyPart string
	if len(parts) > 1 {
		bodyPart = strings.Join(parts[1:], "\r\n\r\n")
	}
	
	// 解析请求头
	lines := strings.Split(headerPart, "\r\n")
	if len(lines) < 1 {
		log.Printf("无效的HTTP请求头")
		return requestData
	}
	
	// 获取请求行
	requestLine := lines[0]
	
	// 构建新的请求头，修改Host
	var newHeaders []string
	newHeaders = append(newHeaders, requestLine)
	
	// 添加修改后的Host头
	newHeaders = append(newHeaders, fmt.Sprintf("Host: localhost:%d", targetPort))
	
	// 添加其他请求头（跳过原始的Host头）
	for i := 1; i < len(lines); i++ {
		line := lines[i]
		if !strings.HasPrefix(strings.ToLower(line), "host:") {
			newHeaders = append(newHeaders, line)
		}
	}
	
	// 重新构建HTTP请求
	newRequest := strings.Join(newHeaders, "\r\n") + "\r\n\r\n" + bodyPart
	
	return newRequest
}

func main() {
	fmt.Println("=== Host请求头修改功能演示 ===")
	
	// 测试用例1: 基本GET请求
	fmt.Println("\n--- 测试用例1: 基本GET请求 ---")
	request1 := "GET /api/chat HTTP/1.1\r\n" +
		"Host: proxy.example.com:8080\r\n" +
		"User-Agent: curl/7.68.0\r\n" +
		"Accept: */*\r\n" +
		"\r\n"
	
	fmt.Println("原始请求:")
	fmt.Print(request1)
	
	modified1 := modifyHostHeader(request1, 3000)
	fmt.Println("\n修改后请求:")
	fmt.Print(modified1)
	
	// 测试用例2: POST请求带请求体
	fmt.Println("\n--- 测试用例2: POST请求带请求体 ---")
	request2 := "POST /api/chat HTTP/1.1\r\n" +
		"Host: api.service.com\r\n" +
		"Content-Type: application/json\r\n" +
		"Content-Length: 20\r\n" +
		"User-Agent: MyApp/1.0\r\n" +
		"\r\n" +
		`{"message": "hello"}`
	
	fmt.Println("原始请求:")
	fmt.Print(request2)
	
	modified2 := modifyHostHeader(request2, 8080)
	fmt.Println("\n修改后请求:")
	fmt.Print(modified2)
	
	// 验证Host头修改的正确性
	fmt.Println("\n=== 验证结果 ===")
	
	testCases := []struct {
		name     string
		original string
		modified string
		port     int
	}{
		{"基本GET请求", request1, modified1, 3000},
		{"POST请求带请求体", request2, modified2, 8080},
	}
	
	for _, tc := range testCases {
		fmt.Printf("\n--- %s ---\n", tc.name)
		
		// 检查是否包含正确的Host头
		expectedHost := fmt.Sprintf("Host: localhost:%d", tc.port)
		if strings.Contains(tc.modified, expectedHost) {
			fmt.Printf("✅ Host头修改正确: %s\n", expectedHost)
		} else {
			fmt.Printf("❌ Host头修改失败，期望: %s\n", expectedHost)
		}
		
		// 检查是否保留了其他头部
		originalLines := strings.Split(strings.Split(tc.original, "\r\n\r\n")[0], "\r\n")
		modifiedLines := strings.Split(strings.Split(tc.modified, "\r\n\r\n")[0], "\r\n")
		
		// 统计非Host头的数量
		originalNonHostHeaders := 0
		for _, line := range originalLines[1:] { // 跳过请求行
			if !strings.HasPrefix(strings.ToLower(line), "host:") {
				originalNonHostHeaders++
			}
		}
		
		modifiedNonHostHeaders := len(modifiedLines) - 2 // 减去请求行和新的Host头
		
		if originalNonHostHeaders == modifiedNonHostHeaders {
			fmt.Printf("✅ 其他请求头保留完整 (%d个)\n", originalNonHostHeaders)
		} else {
			fmt.Printf("❌ 请求头数量不匹配，原始: %d, 修改后: %d\n", originalNonHostHeaders, modifiedNonHostHeaders)
		}
		
		// 检查请求体是否保持不变
		originalBody := ""
		modifiedBody := ""
		
		if strings.Contains(tc.original, "\r\n\r\n") {
			originalBody = strings.SplitN(tc.original, "\r\n\r\n", 2)[1]
		}
		if strings.Contains(tc.modified, "\r\n\r\n") {
			modifiedBody = strings.SplitN(tc.modified, "\r\n\r\n", 2)[1]
		}
		
		if originalBody == modifiedBody {
			fmt.Printf("✅ 请求体保持不变\n")
		} else {
			fmt.Printf("❌ 请求体发生变化\n")
			fmt.Printf("   原始: %q\n", originalBody)
			fmt.Printf("   修改: %q\n", modifiedBody)
		}
	}
	
	fmt.Println("\n=== 演示完成 ===")
}
