package main

import (
	"bufio"
	"bytes"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"os"
	"strings"
	"sync"
	"time"

	util "socks/client/util"
)

// Message format for TCP communication
type Message struct {
	ID   string `json:"id"`
	Type string `json:"type"`
	Data []byte `json:"data,omitempty"`
}

// URLProxyMessage URL代理消息结构
type URLProxyMessage struct {
	ID        string            `json:"id"`                   // 请求 ID
	Type      string            `json:"type"`                 // proxy_request, proxy_response
	BaseURL   string            `json:"base_url,omitempty"`   // URL路径
	TargetURL string            `json:"target_url,omitempty"` // 目标URL
	Method    string            `json:"method,omitempty"`     // HTTP方法
	Headers   map[string]string `json:"headers,omitempty"`    // HTTP头
	Body      []byte            `json:"body,omitempty"`       // 请求/响应体
	Status    int               `json:"status,omitempty"`     // HTTP状态码（响应时使用）
	Error     string            `json:"error,omitempty"`      // 错误信息

	// 流式传输相关字段
	IsStream    bool   `json:"is_stream,omitempty"`     // 是否为流式传输
	StreamType  string `json:"stream_type,omitempty"`   // 流类型：stream_start, stream_chunk, stream_end
	ChunkIndex  int    `json:"chunk_index,omitempty"`   // 数据块索引
	IsLastChunk bool   `json:"is_last_chunk,omitempty"` // 是否为最后一个数据块
}

// SafeConn now wraps a TCP connection instead of WebSocket
type SafeConn struct {
	Conn net.Conn
	wmu  sync.Mutex
	enc  *json.Encoder
	dec  *json.Decoder
	bw   *bufio.Writer // 添加缓冲写入器
}

func (c *SafeConn) WriteJSON(v interface{}) error {
	c.wmu.Lock()
	defer c.wmu.Unlock()
	err := c.enc.Encode(v)
	if err == nil && c.bw != nil {
		err = c.bw.Flush()
	}
	if bw, ok := c.Conn.(interface{ Flush() error }); ok {
		bw.Flush()
	}
	return err
}

func (c *SafeConn) ReadJSON(v interface{}) error {
	return c.dec.Decode(v)
}

func (c *SafeConn) Close() error {
	return c.Conn.Close()
}

// Config 配置信息
type Config struct {
	HostName   string
	ServerIP   string
	ServerPort string
	LocalHost  string
	APIPort    int
	UUID       string
	Type       string
}

// ConnectionManager 连接管理器
type ConnectionManager struct {
	conns map[string]net.Conn
	mu    sync.Mutex
}

type UrlMapping struct {
	Host string
	Port int
}

// PortMapping updated to use TCP connection
type PortMapping struct {
	LocalPort  int                // Local port
	RemotePort int                // Remote port
	Conn       *SafeConn          // TCP connection
	CM         *ConnectionManager // Connection manager
	Created    time.Time          // Creation time
}

type URLRegisterRequest struct {
	ApiType      string `json:"api_type"`      // Agent, AI, or API
	ServiceGroup string `json:"service_group"` // 服务组别
	ServiceName  string `json:"service_name"`  // 服务名称
	ClientName   string `json:"client_name"`   // 客户端名称
	ClientUUID   string `json:"client_id"`     // 客户端UUID
	BaseURL      string `json:"base_url"`      // 基础URL，客户端服务的基础路径
}

type URLRegisterResponse struct {
	Success bool   `json:"success"`
	URLPath string `json:"url_path"`
}

// 全局映射管理
var (
	// 存储所有活跃的端口映射
	portMappings    = make(map[int]*PortMapping) // 本地端口 -> 映射信息
	portMappingsMux sync.Mutex

	urlMappings    = make(map[string]*UrlMapping)
	urlMappingsMux sync.Mutex
)

// 全局控制连接
var (
	globalConn *SafeConn

	// 全局连接管理器
	globalCM *ConnectionManager

	globalPortMappings    = make(map[int]*PortMapping) // 服务端口 -> 映射信息
	globalPortMappingsMux sync.RWMutex
)

// NewConnectionManager 创建连接管理器
func NewConnectionManager() *ConnectionManager {
	return &ConnectionManager{
		conns: make(map[string]net.Conn),
	}
}

// Add 添加连接
func (cm *ConnectionManager) Add(id string, conn net.Conn) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.conns[id] = conn
}

// Remove 移除连接
func (cm *ConnectionManager) Remove(id string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	if conn, ok := cm.conns[id]; ok {
		conn.Close()
		delete(cm.conns, id)
	}
}

// Get 获取连接
func (cm *ConnectionManager) Get(id string) net.Conn {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	return cm.conns[id]
}

// parseFlags 解析命令行参数
func parseFlags() (*Config, error) {
	config := &Config{
		HostName: util.GetHostName(),
		UUID:     util.GenerateUUID(),
	}
	flag.StringVar(&config.ServerIP, "server", "", "中转服务器IP")
	flag.StringVar(&config.ServerPort, "port", "", "中转服务器端口")
	flag.StringVar(&config.LocalHost, "host", "127.0.0.1", "本地服务IP")
	flag.StringVar(&config.Type, "type", "ai", "客户端类型")
	flag.IntVar(&config.APIPort, "manager", 8090, "API服务端口")
	flag.Parse()

	if config.ServerIP == "" || config.ServerPort == "" {
		return nil, fmt.Errorf("必需的参数未提供")
	}
	return config, nil
}

// registerURLMapping 向服务器注册URL映射
func registerURLMapping(config *Config, baseURL, serviceName, serviceGroup, apiType string) (*URLRegisterResponse, error) {
	// 构建请求体

	requestBody := URLRegisterRequest{
		ApiType:      apiType,
		ServiceGroup: serviceGroup, // 使用默认组，也可以让调用者传入
		ServiceName:  serviceName,
		ClientName:   config.HostName,
		ClientUUID:   config.UUID,
		BaseURL:      baseURL,
	}

	requestData, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %v", err)
	}

	// 构建请求URL
	url := fmt.Sprintf("http://%s:%s/url/register",
		config.ServerIP, config.ServerPort)

	// 发送POST请求
	resp, err := http.Post(url, "application/json", bytes.NewBuffer(requestData))
	if err != nil {
		return nil, fmt.Errorf("注册URL映射失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("注册URL映射失败: %s", string(body))
	}

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应体失败: %w", err)
	}
	// 解析响应体
	var response *URLRegisterResponse
	if err := json.Unmarshal(bodyBytes, &response); err != nil {
		// JSON 解析失败
		return nil, fmt.Errorf("解析JSON响应失败: %w", err)
	}

	log.Printf("成功注册URL映射: %s", response.URLPath)
	return response, nil
}

// unregisterURLMapping 向服务器取消注册URL映射
func unregisterURLMapping(config *Config, urlPath string) error {
	// 构建请求URL
	url := fmt.Sprintf("http://%s:%s/url/unregister?client_id=%s&url_path=%s",
		config.ServerIP, config.ServerPort, config.UUID, urlPath)

	req, err := http.NewRequest("DELETE", url, nil)
	if err != nil {
		return fmt.Errorf("创建取消注册请求失败: %v", err)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("取消注册URL映射失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("取消注册URL映射失败: %s", string(body))
	}

	log.Printf("成功取消注册URL映射: %s", urlPath)
	return nil
}

// isStreamResponse 检测响应是否为流式响应
func isStreamResponse(resp *http.Response) bool {
	contentType := resp.Header.Get("Content-Type")
	return strings.Contains(contentType, "text/event-stream") ||
		strings.Contains(contentType, "application/x-ndjson") ||
		strings.Contains(contentType, "text/plain") && resp.Header.Get("Transfer-Encoding") == "chunked"
}

// handleNormalResponse 处理普通响应
func handleNormalResponse(conn *SafeConn, requestID string, resp *http.Response, headers map[string]string) {
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("load response fail, err: %v", err)
		sendURLProxyResponse(conn, requestID, 500, nil, nil, fmt.Sprintf("读取响应失败: %v", err))
		return
	}

	// 发送响应
	sendURLProxyResponse(conn, requestID, resp.StatusCode, headers, body, "")
}

// sendURLProxyResponse 发送URL代理响应
func sendURLProxyResponse(conn *SafeConn, requestID string, status int, headers map[string]string, body []byte, errorMsg string) {
	response := URLProxyMessage{
		ID:      requestID,
		Type:    "proxy_response",
		Status:  status,
		Headers: headers,
		Body:    body,
		Error:   errorMsg,
	}

	responseData, err := json.Marshal(response)
	if err != nil {
		log.Printf("序列化响应失败: %v", err)
		return
	}

	// 发送响应消息
	msg := Message{
		ID:   requestID,
		Type: "proxy_response",
		Data: responseData,
	}

	if err := conn.WriteJSON(msg); err != nil {
		log.Printf("发送URL代理响应失败: %v", err)
	} else {
		log.Printf("成功发送URL代理响应: %s (状态码: %d)", requestID, status)
	}
}

func register(config *Config) (*SafeConn, error) {
	// 获取本机IP地址
	localIP, err := util.GetLocalIP()
	if err != nil {
		log.Printf("获取本机IP失败: %v, 使用默认IP", err)
	}

	// 构建请求URL
	serverAddr := fmt.Sprintf("%s:%s", config.ServerIP, config.ServerPort)
	url := fmt.Sprintf("http://%s/register?name=%s&type=%s&id=%s&ip=%s",
		serverAddr, config.HostName, config.Type, config.UUID, localIP)

	log.Printf("正在连接到服务器: %s", url)

	// 创建HTTP请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}

	// 创建TCP连接
	conn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		return nil, fmt.Errorf("连接失败: %v", err)
	}

	// 发送HTTP请求
	err = req.Write(conn)
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("发送HTTP请求失败: %v", err)
	}

	// 读取HTTP响应头
	resp, err := http.ReadResponse(bufio.NewReader(conn), req)
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}
	log.Printf("sever response: %v", resp)
	// 检查响应状态
	if resp.StatusCode != http.StatusSwitchingProtocols {
		body, _ := io.ReadAll(resp.Body)
		conn.Close()
		return nil, fmt.Errorf("意外的状态码: %s, 响应体: %s", resp.Status, string(body))
	}

	// 设置TCP连接参数
	tcpConn := conn.(*net.TCPConn)
	tcpConn.SetKeepAlive(true)
	tcpConn.SetKeepAlivePeriod(30 * time.Second)
	tcpConn.SetWriteBuffer(32 * 1024)
	tcpConn.SetReadBuffer(32 * 1024)
	// 创建安全连接，使用更大的缓冲区
	bw := bufio.NewWriterSize(conn, 32*1024)
	safeConn := &SafeConn{
		Conn: conn,
		bw:   bw,
		enc:  json.NewEncoder(bw),
		dec:  json.NewDecoder(bufio.NewReaderSize(conn, 32*1024)),
	}

	buf := make([]byte, 1024)
	conn.SetReadDeadline(time.Now().Add(5 * time.Second)) // 设置5秒超时
	n, err := conn.Read(buf)
	if err != nil {
		log.Printf("读取服务端确认消息失败: %v", err)
	} else {
		log.Printf("收到服务端确认消息: %s", string(buf[:n]))
	}
	conn.SetReadDeadline(time.Time{}) // 清除超时设置
	log.Printf("成功注册客户端并建立控制连接")

	return safeConn, nil
}

func buildTunnel(config *Config, localPort int, serviceName string) (int, error) {
	// 获取本机IP地址
	localIP, err := util.GetLocalIP()
	if err != nil {
		log.Printf("获取本机IP失败: %v, 使用默认IP", err)
	}

	// 构建请求URL，包含本机IP和服务名称
	url := fmt.Sprintf("http://%s:%s/allocate?id=%s&port=%d&service_name=%s",
		config.ServerIP, config.ServerPort, config.UUID, localPort, serviceName)

	resp, err := http.Get(url)
	if err != nil {
		return 0, fmt.Errorf("申请端口失败: %v", err)
	}
	defer resp.Body.Close()

	// 解析服务端返回的端口信息
	var result struct {
		Port int `json:"port"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return 0, fmt.Errorf("解析响应失败: %v", err)
	}

	log.Printf("成功申请端口映射: 本地端口 %d -> 远程端口 %d (本机IP: %s)",
		localPort, result.Port, localIP)

	return result.Port, nil
}

// handleLocalToWS 处理本地到WS的数据转发
func handleLocalToWS(id string, localConn net.Conn, conn *SafeConn) {
	log.Print("start do handleLocalToWS")
	buf := make([]byte, 4096)
	for {
		n, err := localConn.Read(buf)
		log.Printf("read from local conn: %v", buf[:n])

		if n > 0 {
			conn.WriteJSON(Message{ID: id, Type: "data", Data: buf[:n]})
		}
		if err != nil {
			break
		}
	}
	conn.WriteJSON(Message{ID: id, Type: "close"})
}

// handleURLLocalToWS 处理URL代理本地到WS的数据转发
func handleURLLocalToWS(id string, localConn net.Conn, conn *SafeConn) {
	log.Printf("开始URL代理数据转发: %s", id)
	buf := make([]byte, 4096)
	for {
		n, err := localConn.Read(buf)
		if n > 0 {
			// 直接转发原始HTTP响应数据
			conn.WriteJSON(Message{ID: id, Type: "data", Data: buf[:n]})
		}
		if err != nil {
			log.Printf("URL代理本地连接读取结束: %v", err)
			break
		}
	}
	conn.WriteJSON(Message{ID: id, Type: "close"})
	log.Printf("URL代理数据转发结束: %s", id)
}

// handleOpenMessage 处理打开连接消息
func handleOpenMessage(msg Message, conn *SafeConn, cm *ConnectionManager, localAddr string) {
	localConn, err := net.Dial("tcp", localAddr)
	if err != nil {
		log.Printf("本地连接失败: %v", err)
		conn.WriteJSON(Message{ID: msg.ID, Type: "close"})
		return
	}
	log.Printf("msg.ID: %s", msg.ID)
	cm.Add(msg.ID, localConn)
	log.Printf("add local conn success")

	go handleLocalToWS(msg.ID, localConn, conn)
}

// handleURLOpenMessage 处理URL代理TCP连接打开消息
func handleURLOpenMessage(msg Message, conn *SafeConn, cm *ConnectionManager) {
	// 解析baseURL和targetPath
	data := string(msg.Data)
	parts := strings.Split(data, "|")
	if len(parts) != 2 {
		log.Printf("Invalid url_open message data: %s", data)
		conn.WriteJSON(Message{ID: msg.ID, Type: "close"})
		return
	}

	baseURL := parts[0]
	targetPath := parts[1]

	log.Printf("处理URL代理连接: baseURL=%s, targetPath=%s", baseURL, targetPath)

	// 查找URL映射
	urlMappingsMux.Lock()
	mapping, ok := urlMappings[baseURL]
	urlMappingsMux.Unlock()

	if !ok {
		log.Printf("URL映射未找到: %s", baseURL)
		conn.WriteJSON(Message{ID: msg.ID, Type: "close"})
		return
	}

	// 建立到本地服务的TCP连接
	localAddr := fmt.Sprintf("%s:%d", mapping.Host, mapping.Port)
	localConn, err := net.Dial("tcp", localAddr)
	if err != nil {
		log.Printf("连接本地服务失败: %v", err)
		conn.WriteJSON(Message{ID: msg.ID, Type: "close"})
		return
	}

	log.Printf("成功连接到本地服务: %s", localAddr)
	cm.Add(msg.ID, localConn)

	// 启动数据转发
	go handleURLLocalToWS(msg.ID, localConn, conn)
}

// handleDataMessage 处理数据消息
func handleDataMessage(msg Message, cm *ConnectionManager) {
	if localConn := cm.Get(msg.ID); localConn != nil {
		localConn.Write(msg.Data)
	}
}

// 获取或创建全局控制连接
func buildGlobalCM() {
	globalCM = NewConnectionManager()
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for range ticker.C {
			log.Printf("send time: %v", time.Now())
			err := globalConn.WriteJSON(Message{
				ID:   util.HEARTBEAT,
				Type: "ping",
				Data: []byte("ping"),
			})
			if err != nil {
				log.Printf("心跳发送失败: %v", err)
				return
			}
		}
	}()
	// 启动全局消息处理
	go func() {
		for {
			log.Printf("等待服务端消息...")
			var msg Message
			if err := globalConn.ReadJSON(&msg); err != nil {
				log.Printf("全局连接读取错误: %v", err)
				// 可以在这里添加重连逻辑
				break
			}

			log.Printf("成功读取到消息: Type=%s, ID=%s", msg.Type, msg.ID)
			// 根据消息类型和ID路由到对应的处理函数

			switch msg.Type {
			case "open":
				serverPort, err := util.GetServerPortByMsgId(msg.ID)
				if err != nil {
					log.Printf("msg process err: %v", err)
				}
				// 查找对应的本地端口
				globalPortMappingsMux.RLock()
				defer globalPortMappingsMux.RUnlock()
				var localAddr string
				if mapping, ok := globalPortMappings[serverPort]; ok {
					localAddr = fmt.Sprintf("0.0.0.0:%d", mapping.LocalPort)
					handleOpenMessage(msg, globalConn, globalCM, localAddr)
				} else {
					log.Printf("server port mapping not build, server port: %d", serverPort)
				}
			case "data":
				handleDataMessage(msg, globalCM)

			case "close":
				globalCM.Remove(msg.ID)

			case "ping":
				// 处理心跳消息
				globalConn.WriteJSON(Message{ID: msg.ID, Type: "pong"})
			case "pong":
				log.Print("get pong")
			case "proxy_request":
				// 处理URL代理请求 (已废弃，现在使用TCP转发模式)
				// handleURLProxyRequest(msg, globalConn)
				log.Printf("收到废弃的proxy_request消息，忽略")
			case "url_open":
				// 处理URL代理TCP连接打开请求
				handleURLOpenMessage(msg, globalConn, globalCM)
			}
		}
		log.Print("stop conn!!!!!")

	}()

}

// buildPortMapping 新的端口映射逻辑，复用全局控制连接和连接管理器
func buildPortMapping(config *Config, localPort int, serviceName string) (*PortMapping, error) {
	// 检查映射是否已存在
	portMappingsMux.Lock()
	defer portMappingsMux.Unlock()
	if mapping, exists := portMappings[localPort]; exists {
		return mapping, nil
	}

	// 申请远程端口
	remotePort, err := buildTunnel(config, localPort, serviceName)
	if err != nil {
		return nil, fmt.Errorf("申请远程端口失败: %v", err)
	}
	log.Printf("成功申请端口映射: 本地端口 %d -> 远程端口 %d", localPort, remotePort)

	// 创建映射记录，使用全局连接管理器
	mapping := &PortMapping{
		LocalPort:  localPort,
		RemotePort: remotePort,
		Conn:       globalConn, // 复用全局连接
		CM:         globalCM,   // 复用全局连接管理器
		Created:    time.Now(),
	}

	// 保存映射
	globalPortMappingsMux.Lock()
	globalPortMappings[remotePort] = mapping
	globalPortMappingsMux.Unlock()

	return mapping, nil
}

// startAPIServer 启动API服务器
func startAPIServer(config *Config) {

	// 状态端点
	http.HandleFunc("/status", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		type MappingInfo struct {
			LocalPort  int       `json:"local_port"`
			RemotePort int       `json:"remote_port"`
			Created    time.Time `json:"created"`
		}

		var mappings []MappingInfo

		portMappingsMux.Lock()
		for _, mapping := range portMappings {
			mappings = append(mappings, MappingInfo{
				LocalPort:  mapping.LocalPort,
				RemotePort: mapping.RemotePort,
				Created:    mapping.Created,
			})
		}
		portMappingsMux.Unlock()

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"server":   fmt.Sprintf("%s:%s", config.ServerIP, config.ServerPort),
			"mappings": mappings,
			"count":    len(mappings),
		})
	})

	http.HandleFunc("/tunnel", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost && r.Method != http.MethodGet {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// 获取本地端口
		portStr := r.URL.Query().Get("port")
		if portStr == "" {
			http.Error(w, "Missing port parameter", http.StatusBadRequest)
			return
		}

		localPort := 0
		if _, err := fmt.Sscanf(portStr, "%d", &localPort); err != nil || localPort <= 0 || localPort > 65535 {
			http.Error(w, "Invalid port number", http.StatusBadRequest)
			return
		}

		serviceName := r.URL.Query().Get("service_name")

		// 设置映射
		mapping, err := buildPortMapping(config, localPort, serviceName)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// 返回映射信息
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"local_port":  mapping.LocalPort,
			"remote_port": mapping.RemotePort,
			"server":      config.ServerIP,
			"created":     mapping.Created,
		})
	})

	// URL注册端点
	http.HandleFunc("/url/register", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		type URLRegisterRequest struct {
			Name    string `json:"name"`
			Group   string `json:"group"`
			Type    string `json:"type"`
			Port    int    `json:"port"`
			BaseURL string `json:"url"`
		}

		var req URLRegisterRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			http.Error(w, "illegal request body", http.StatusBadRequest)
			return
		}

		if req.Name == "" {
			http.Error(w, "Missing name parameter", http.StatusBadRequest)
			return
		}

		if req.Group == "" {
			http.Error(w, "Missing group parameter", http.StatusBadRequest)
			return
		}

		if req.Type == "" {
			http.Error(w, "Missing type parameter", http.StatusBadRequest)
			return
		}

		if req.Port <= 0 || req.Port > 65535 {
			http.Error(w, "Missing port parameter", http.StatusBadRequest)
			return
		}

		urlMappingsMux.Lock()
		defer urlMappingsMux.Unlock()
		if _, ok := urlMappings[req.BaseURL]; ok {
			http.Error(w, "url already registed", http.StatusBadRequest)
			return
		}

		// 向server注册URL映射
		response, err := registerURLMapping(config, req.BaseURL, req.Name, req.Group, util.GetFormatServiceType(req.Type))
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		urlMappings[req.BaseURL] = &UrlMapping{Host: config.LocalHost, Port: req.Port}

		// 返回成功响应
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	})

	// URL取消注册端点
	http.HandleFunc("/url/unregister", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodDelete {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		// 获取参数
		baseURL := r.URL.Query().Get("url")
		if baseURL == "" {
			http.Error(w, "Missing url_path parameter", http.StatusBadRequest)
			return
		}

		// 向server取消注册URL映射
		err := unregisterURLMapping(config, baseURL)
		if err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}

		// 返回成功响应
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]interface{}{
			"success":  true,
			"url_path": baseURL,
		})
	})

	// 启动API服务器
	apiAddr := fmt.Sprintf("%s:%d", config.LocalHost, config.APIPort)
	log.Printf("API服务器启动在 %s", apiAddr)
	if err := http.ListenAndServe(apiAddr, nil); err != nil {
		log.Fatalf("API服务器启动失败: %v", err)
	}
}

func main() {
	// 解析命令行参数
	config, err := parseFlags()
	if err != nil {
		flag.Usage()
		os.Exit(1)
	}

	log.Printf("开始注册客户端...")
	globalConn, err = register(config)
	if err != nil {
		log.Printf("register client error: %v", err)
		os.Exit(1)
	}
	log.Printf("客户端注册成功，开始构建全局连接管理器...")

	buildGlobalCM()
	log.Printf("全局连接管理器构建完成，启动API服务器...")

	// 启动API服务器
	startAPIServer(config)

}
